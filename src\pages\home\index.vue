<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2025-07-28 09:25:55
 * @LastEditors: wjb
 * @LastEditTime: 2025-07-30 17:48:51
-->
<template>
  <div class="container"></div>
</template>

<script>
export default {
  name: 'index',
  components: {},
  data() {
    return {}
  },
  mounted() {
    this.$nextTick(() => {})
  },
  computed: {},
  methods: {},
}
</script>

<style lang="less" scoped>
</style>
