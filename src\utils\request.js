import axios from 'axios'
import { getCookie } from '@/utils/get-cookie'
axios.defaults.timeout = 60000
axios.defaults.withCredentials = true
axios.defaults.headers['Authorization'] = getCookie('Admin-Token')
  ? getCookie('Admin-Token')
  : 'eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6IjIxOTllMjUxLTY5NmEtNGMwYi04ZmI3LWY1MmFlZmY0YTc3YSJ9.A35Dx7GEe8xnzbqODGrOkNlH3jQIvn1U3BKJ5nngHJzfQzEad0w5MQJU91cbHyR6F4bjbgRaSwTj6ZQDSj4ojw' // 让每个请求携带自定义token 请根据实际情况自行修改

const METHOD = {
  GET: 'get',
  POST: 'post',
}

/**
 * axios请求
 * @param url 请求地址
 * @param method {METHOD} http method
 * @param params 请求参数
 * @returns {Promise<AxiosResponse<T>>}
 */
async function request(url, method, params, config) {
  url = process.env.VUE_APP_BASE_URL + url
  let promise = Promise.resolve(true)
  switch (method) {
    case METHOD.GET:
      promise = axios.get(url, { params, ...config })
      break
    case METHOD.POST:
      promise = axios.post(url, params, config)
      break
    default:
      promise = axios.get(url, { params, ...config })
      break
  }
  return promise.then((res) => {
    return {
      status: res.status,
      statusText: res.statusText,
      data: res.data,
    }
  })
}

async function request2(url, method, params, config) {
  let promise = Promise.resolve(true)
  switch (method) {
    case METHOD.GET:
      promise = axios.get(url, { params, ...config })
      break
    case METHOD.POST:
      promise = axios.post(url, params, config)
      break
    default:
      promise = axios.get(url, { params, ...config })
      break
  }
  return promise.then((res) => {
    return {
      status: res.status,
      statusText: res.statusText,
      data: res.data,
    }
  })
}

async function requestAfdc(url, method, params, config) {
  url = process.env.VUE_APP_BASE_AFDC_URL + url
  let promise = Promise.resolve(true)
  switch (method) {
    case METHOD.GET:
      promise = axios.get(url, { params, ...config })
      break
    case METHOD.POST:
      promise = axios.post(url, params, config)
      break
    default:
      promise = axios.get(url, { params, ...config })
      break
  }
  return promise.then((res) => {
    return {
      status: res.status,
      statusText: res.statusText,
      data: res.data,
    }
  })
}

export { request, request2, requestAfdc }
