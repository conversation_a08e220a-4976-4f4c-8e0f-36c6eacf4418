<template>
  <div>
    <div class="item" v-for="(item, i) in list" :key="i">
      <div class="flex-b">
        <div class="flex-c">
          <img :src="item.icon" class="icon" />
          <div class="name">{{ item.name }}</div>
        </div>
        <div class="num">{{ item.value }}{{ item.unit }}</div>
      </div>
      <img src="@/assets/img/home/<USER>/divider_horizontal.png" class="divider-h" />
      <div class="itemList flex-b">
        <div class="liBox flex-b" v-for="(value, key, j) in item.itemList" :key="key">
          <!-- :style="{ width: 460 / Object.keys(item.itemList).length + 'px' }" -->
          <div class="li" :style="{ width: i == 0 ? '120px' : '70px' }">
            <div class="key">{{ key }}</div>
            <div class="value">{{ value }}</div>
          </div>
          <img
            v-if="j !== Object.keys(item.itemList).length - 1"
            src="@/assets/img/home/<USER>/divider_horizontal.png"
            class="divider-v"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [
        {
          name: '应用总数',
          value: '1.41',
          unit: '万',
          icon: require('@/assets/img/home/<USER>/yyzs.png'),
          itemList: { IP总数: '4.23万', 数据总数: '3.45万', 运维人员总数: '187人' },
        },
        {
          name: '云资源总数',
          value: '1.41',
          unit: '万',
          icon: require('@/assets/img/home/<USER>/yzyzs.png'),
          itemList: { ECS: '1876', RDS: '2896', SLB: '3987', OSS: '3987' },
        },
      ],
    }
  },
}
</script>

<style lang="less" scoped>
.item {
  background: linear-gradient(to right, #026ef100, #026ef13b, #026ef100);
  margin: 10px 0 16px 0;
  padding: 12px;
  box-sizing: border-box;
  .img {
    width: 25px;
    height: 28px;
  }
  .name {
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 400;
    font-size: 20px;
    color: #ffffff;
    line-height: 16px;
    text-align: left;
    margin-left: 14px;
  }
  .num {
    width: 80px;
    height: 35px;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 24px;
    line-height: 35px;
    text-align: center;
    background: linear-gradient(180deg, #ffffff 0%, #b9ccff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .itemList {
    padding-top: 12px;
    box-sizing: border-box;
    .liBox {
      // width: 100%;
      flex: 1;
      .li {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        // padding: 0 50px;
        box-sizing: border-box;
        position: relative;
        // &::after {
        //   content: '';
        //   position: absolute;
        //   top: 0;
        //   right: 0;
        //   width: 1px;
        //   height: 60px;
        //   background: url('@/assets/img/home/<USER>/divider_vertical.png');
        //   background-size: 100% 100%;
        // }
        .key {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 16px;
          color: #ffffff;
          line-height: 23px;
          text-align: center;
        }
        .value {
          margin-top: 4px;
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 700;
          font-size: 18px;
          line-height: 26px;
          background: linear-gradient(180deg, #ffffff 0%, #b9ccff 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          text-align: center;
        }
      }
      // .li:not(:last-child)::after {
      //   content: '';
      //   position: absolute;
      //   top: 0;
      //   right: 0;
      //   width: 1px;
      //   height: 60px;
      //   background: url('@/assets/img/home/<USER>/divider_vertical.png') no-repeat center;
      //   background-size: 100% 100%;
      // }
    }
  }
}
.divider-h {
  width: 460px;
  height: 1px;
}
.divider-v {
  width: 60px;
  height: 1px;
  transform: rotate(90deg);
}
</style>