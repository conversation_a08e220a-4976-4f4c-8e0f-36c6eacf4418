import { request, request2 } from '@/utils/request'

// 查询事件基本信息列表
export const getEventList = (params) => {
  return request2(`/jazz-api/bigScreen/page/eventList`, 'get', params)
}

// 获取事件基本信息详细信息
export const getEventDetails = (id) => {
  return request2(`/jazz-api/bigScreen/page/event/${id}`, 'get')
}

// 地图的市区统计
export const getRegionStatistics = (params) => {
  return request2(`/jazz-api/bigScreen/page/map/regionStatistics`, 'get', params)
}

// 区域底库人员详情
export const getRegionPerson = (params) => {
  return request2(`/jazz-api/bigScreen/page/map/regionPerson`, 'get', params)
}
// 区域考核晾晒
export const getRegionExamine = (params) => {
  return request2(`/jazz-api/bigScreen/page/map/regionExamine`, 'get', params)
}

// 区域重大事件详情
export const getRegionEvent = (params) => {
  return request2(`/jazz-api/bigScreen/page/map/regionEvent`, 'get', params)
}

// 当日交办
export const getTodayAssigned = (params) => {
  return request2(`/jazz-api/bigScreen/page/map/todayAssigned`, 'get', params)
}

// 当日已交办列表
export const getTodayAssignedDetails = (params) => {
  return request2(`/jazz-api/bigScreen/page/map/todayAssignedDetails`, 'get', params)
}

// 当日已销号列表
export const getTodayEndDetails = (params) => {
  return request2(`/jazz-api/bigScreen/page/map/todayEndDetails`, 'get', params)
}

// 指令列表(没有权限)
export const getInstructionListNoPower = (params) => {
  return request2(`/jazz-api/bigScreen/page/instructionListNoPower`, 'get', params)
}

// 获取视频相关数据接口
export const getDahuaWater = () => {
  return request2(`/jazz-api/system/dahuaWater/list`, 'get')
}

// 根据场景获取视频相关数据接口
export const getSceenVideo = () => {
  return request2(`/jazz-api/instruction/video/getScene`, 'get')
}

// 获取暗访点位相关数据接口
export const getafdw = () => {
  return request2(`/jazz-api/bigScreen/gkPerson/category`, 'get')
}

//获取随机六个大华视频点位
export const getRandomVideos = () => {
  return request2(`/jazz-api/instruction/video/getRandomVideos`, 'get')
}
