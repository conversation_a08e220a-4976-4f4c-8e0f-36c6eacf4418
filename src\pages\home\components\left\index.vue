<template>
  <div class="left-container">
    <FirstTitle :title="'资产概况'"></FirstTitle>
    <zcgk style="margin-bottom: 28px"></zcgk>
    <FirstTitle :title="'云产品水位'"></FirstTitle>
    <ycpsw></ycpsw>
  </div>
</template>

<script>
import FirstTitle from '@/components/FirstTitle/index.vue'
import zcgk from '@/pages/home/<USER>/left/components/zcgk.vue'
import ycpsw from '@/pages/home/<USER>/left/components/ycpsw.vue'
export default {
  components: { FirstTitle, zcgk, ycpsw },

  data() {
    return {}
  },
}
</script>

<style lang="less" scoped>
.left-container {
  width: 500px;
  height: 950px;
  background-color: #0b13204d;
}
</style>