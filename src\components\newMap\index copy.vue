<template>
  <div class="map-container" :style="{ width: width, height: height }">
    <div
      class="map"
      id="viewDiv"
      :style="`width: ${width}; height: ${height}; transform: scale(${scaleWidth}, ${scaleHeight})`"
    ></div>

    <slot></slot>
    <div class="yycgButton" v-show="isShowYycg">
      <img class="yycg" src="@/assets/img/common/yycg.png" alt="" @click="yycgClick()" />
      <img v-show="yycgShow" class="yycg-icon" src="@/assets/img/common/spjk-icon.png" alt="" />
    </div>

    <div class="cgyjSelector" v-show="yycgShow">
      <div style="width: 112px; height: 159px">
        <el-tree
          node-key="id"
          :default-expanded-keys="[2]"
          :data="newArr2"
          show-checkbox
          ref="tree"
          :render-after-expand="false"
          @check-change="checkChange1"
        >
          <div style="display: flex" slot-scope="{ node, data }">
            <div
              style="
                line-height: 3.125rem;
                font-size: 14px;
                font-weight: bold;
                font-family: PangMenZhengDao;
                color: rgb(192, 214, 237);
                line-height: 58px;
              "
            >
              {{ node.label }}
            </div>
          </div>
        </el-tree>
      </div>
    </div>

    <div class="yjtzButton" v-show="isShowYycg">
      <img class="yjtz" src="@/assets/img/common/yjtz.png" alt="" @click="yjtzClick()" />
    </div>

    <div class="spjkButton" v-show="isShowVideos">
      <img class="spjk" src="@/assets/img/common/spjk.png" alt="" @click="spjkClick()" />
      <img v-show="spjkShow" class="spjk-icon" src="@/assets/img/common/spjk-icon.png" alt="" />
    </div>

    <div class="spjkSelector" v-show="spjkShow">
      <div style="width: 218px; height: 316px; overflow: scroll">
        <el-tree
          node-key="id"
          :default-expanded-keys="[42]"
          :data="newArr1"
          show-checkbox
          ref="tree"
          :render-after-expand="false"
          @check-change="checkChange"
        >
          <div style="display: flex" slot-scope="{ node, data }">
            <div
              style="
                line-height: 3.125rem;
                font-size: 14px;
                font-weight: bold;
                font-family: PangMenZhengDao;
                color: rgb(192, 214, 237);
                line-height: 58px;
              "
            >
              {{ node.label }}
            </div>
          </div>
        </el-tree>
      </div>
    </div>

    <div class="afdwButton" v-show="isShowAfdw">
      <img class="afdw" src="@/assets/img/common/暗访点位@2x.png" alt="" @click="afdwClick()" />
      <img v-show="afdwShow" class="afdw-icon" src="@/assets/img/common/spjk-icon.png" alt="" />
    </div>

    <div class="afdwSelector" v-show="afdwShow">
      <div style="width: 175px; height: 292px; overflow: scroll">
        <el-tree
          node-key="id"
          :default-expanded-keys="[0]"
          :data="afdwData"
          show-checkbox
          ref="tree"
          :render-after-expand="false"
          @check-change="afdwChange"
        >
          <div style="display: flex" slot-scope="{ node, data }">
            <div
              style="
                line-height: 3.125rem;
                font-size: 14px;
                font-weight: bold;
                font-family: PangMenZhengDao;
                color: rgb(192, 214, 237);
                line-height: 58px;
              "
            >
              {{ node.label }}
            </div>
          </div>
        </el-tree>
      </div>
    </div>

    <div class="hjButton" v-show="isShowLines">
      <img class="hj" src="@/assets/img/common/hj.png" alt="" @click="hjClick()" />
      <img v-show="hjShow" class="hj-icon" src="@/assets/img/common/hj-icon.png" alt="" />
    </div>

    <div class="zlimg" @click="goDirective" v-show="isShowFunction">
      <div class="zlimg1">
        <img class="imgMsg" src="@/assets/img/common/zhiling3.png" alt="" />
      </div>
    </div>

    <div class="radioDiv" v-show="isShowFunction">
      <div class="radioitem" v-for="(item, i) in radioArr" :key="i + 'a'" @click="clickRadio(i)">
        <div :class="{ radioActive: radioIndex == i }">{{ radioIndex == i ? '√' : '' }}</div>
        <div>{{ item }}</div>
      </div>
    </div>

    <div class="full" v-show="showFullFuc">
      <img src="@/assets/img/map/展开.png" alt="" v-show="loadStatus == false" class="fullImg" @click="fullMap(true)" />
      <img src="@/assets/img/map/收起.png" alt="" v-show="loadStatus == true" class="fullImg" @click="fullMap(false)" />
    </div>

    <!--    <div class="back" v-show="area != '' && isLoad" @click="goTop">-->
    <!--      <img src="@/assets/img/jazz/common/row.png" alt="" v-show="area != ''" />-->
    <!--      <div class="areaText">{{ area }}</div>-->
    <!--    </div>-->
    <div class="video_list_bottom" v-if="loadStatus && listData.length > 0">
      <div class="show_more" v-show="!showVideoList">
        <div class="txt cursor" @click="videoClick"></div>
      </div>
      <div class="video_list" v-show="showVideoList">
        <div class="video_list_btn cursor" @click="videoClick"></div>
        <div class="video_bottom_box">
          <!-- v-for="(item, index) in listData" :key="index" -->
          <div class="video_bottom_item" v-show="listData.length > 0">
            <venueVideo
              width="280px"
              height="210px"
              :videoConfig="videoConfigList[0]"
              :code="codeList[0]"
              :createflag="createflag"
              :setflag="setflag"
              :destoryflag="destoryflag"
              :visible="showVideoList"
            ></venueVideo>
          </div>
          <div class="video_bottom_item" v-show="listData.length > 1">
            <venueVideo
              width="280px"
              height="210px"
              :videoConfig="videoConfigList[1]"
              :code="codeList[1]"
              :createflag="createflag"
              :setflag="setflag"
              :destoryflag="destoryflag"
              :visible="showVideoList"
            ></venueVideo>
          </div>
          <div class="video_bottom_item" v-show="listData.length > 2">
            <venueVideo
              width="280px"
              height="210px"
              :videoConfig="videoConfigList[2]"
              :code="codeList[2]"
              :createflag="createflag"
              :setflag="setflag"
              :destoryflag="destoryflag"
              :visible="showVideoList"
            ></venueVideo>
          </div>
          <div class="video_bottom_item" v-show="listData.length > 3">
            <venueVideo
              width="280px"
              height="210px"
              :videoConfig="videoConfigList[3]"
              :code="codeList[3]"
              :createflag="createflag"
              :setflag="setflag"
              :destoryflag="destoryflag"
              :visible="showVideoList"
            ></venueVideo>
          </div>
          <div class="video_bottom_item" v-show="listData.length > 4">
            <venueVideo
              width="280px"
              height="210px"
              :videoConfig="videoConfigList[4]"
              :code="codeList[4]"
              :createflag="createflag"
              :setflag="setflag"
              :destoryflag="destoryflag"
              :visible="showVideoList"
            ></venueVideo>
          </div>
          <div class="video_bottom_item" v-show="listData.length > 5">
            <venueVideo
              width="280px"
              height="210px"
              :videoConfig="videoConfigList[5]"
              :code="codeList[5]"
              :createflag="createflag"
              :setflag="setflag"
              :destoryflag="destoryflag"
              :visible="showVideoList"
            ></venueVideo>
          </div>
        </div>
      </div>
    </div>

    <!--视频点位弹窗-->
    <CommonDialog
      :dialogFlag="videoDialogFlag"
      :dialog-width="'1076px'"
      :title="title"
      @close="videoDialogFlag = false"
      :tabArrFlag="false"
    >
      <div class="video_box">
        <div class="video_item">
          <venueVideo
            ref="video"
            width="624px"
            height="624px"
            :visible="visible"
            :videoConfig="videoConfig"
            :code="code"
            :createflag="createflag"
            :setflag="setflag"
            :destoryflag="destoryflag"
          ></venueVideo>
        </div>
        <div class="line"></div>
        <div class="infos">
          <div class="info">
            <div class="name">设备名称：</div>
            <div class="value">{{ info.value }}</div>
          </div>
          <div class="info">
            <div class="name">所属区域：</div>
            <div class="value">{{ info.orgname }}</div>
          </div>
          <div class="jiajian">
            <img
              src="@/assets/img/map/right.png"
              alt=""
              height="50"
              v-show="titleListShow"
              :class="leftDisabled ? 'disPoint' : 'point'"
              @click="prevStep"
            />
            <div>
              <span v-show="titleListShow" style="line-height: 45px; color: #00c0ff; font-size: 20px">
                ({{ pointIndex + 1 }}/{{ pointList.length }})
              </span>
            </div>
            <img
              src="@/assets/img/map/left.png"
              alt=""
              height="50"
              v-show="titleListShow"
              :class="rightDisabled ? 'disPoint' : 'point'"
              @click="nextStep"
            />
          </div>
        </div>
      </div>
    </CommonDialog>
  </div>
</template>

<script>
import geoJson from '../../../public/data/bounds.json'
import DHWs from '../../components/Video/DHWs.js'
const DHWsInstance = DHWs.getInstance({
  reConnectCount: 2,
  connectionTimeout: 30 * 1000000,
  messageEvents: {
    loginState() {
      console.log('aaaa')
    },
  },
})
import VenueVideo from '../../components/Video/VenueVideo'
import CommonDialog from '@/components/Commondialog/index.vue'
import { getafdw, getSceenVideo, getRegionStatistics, getDahuaWater } from '@/api/home'
import { request2 } from '../../utils/request'
export default {
  data() {
    return {
      typeList: [
        '公共娱乐场所',
        '出租房',
        '旅馆民宿（农家乐）',
        '加油站',
        '小餐饮',
        '商场市场',
        '寄递业',
        '医院诊所药店',
        '公交车',
        '建筑工地',
        '企业',
        '学校（幼儿园）',
        '高层住宅小区',
        '食品小作坊',
        '培训机构',
        '车站（高铁站）',
        '网吧',
        '文化出版市场',
        '瓶装燃气单位',
        '沿街店面',
        '主干道路口',
        '诊所药店',
      ],
      pointList: [],
      pointIndex: 0,
      titleListShow: true,
      leftDisabled: true,
      rightDisabled: true,
      newArr2: [
        {
          id: 1,
          label: '亚运场馆',
        },
        {
          id: 2,
          label: '应急点位',
          children: [
            {
              id: 3,
              label: '市本级',
              esX: '119.62511848362585',
              esY: '29.103237435903115',
            },
            {
              id: 4,
              label: '婺城区',
            },
            {
              id: 5,
              label: '金东区',
              esX: 119.6760129168193,
              esY: 29.093918415717205,
            },
            {
              id: 6,
              label: '开发区',
              esX: 119.6378301197201,
              esY: 29.028459786760138,
            },
          ],
        },
      ],
      newArr1: [],
      afdwData: [],
      scaleWidth: 1,
      scaleHeight: 1,
      deptId: localStorage.getItem('deptId'),
      quhuajson: geoJson,
      urlR: require('@/assets/img/map/right.png'),
      urlL: require('@/assets/img/map/left.png'),
      backurl: require('@/assets/img/map/cl.png'),
      //板块数据
      mapList: [
        { id: 220, name: '婺城区', height: 3000, esX: 119.509748, esY: 28.977012, color: [39, 179, 234, 0.9] },
        { id: 216, name: '义乌市', height: 3000, esX: 120.061011, esY: 29.300614, color: [121, 217, 255, 0.9] },
        { id: 219, name: '金东区', height: 3000, esX: 119.799596, esY: 29.149391, color: [40, 194, 254, 0.9] },
        { id: 215, name: '东阳市', height: 3000, esX: 120.375678, esY: 29.232405, color: [0, 140, 195, 0.9] },
        { id: 218, name: '永康市', height: 3000, esX: 120.102417, esY: 28.934317, color: [45, 96, 157, 0.9] },
        { id: 214, name: '兰溪市', height: 3000, esX: 119.526736, esY: 29.278165, color: [60, 145, 177, 0.9] },
        { id: 262, name: '武义县', height: 3000, esX: 119.714529, esY: 28.768287, color: [50, 104, 195, 0.9] },
        { id: 213, name: '磐安县', height: 3000, esX: 120.559672, esY: 29.037893, color: [7, 88, 231, 0.9] },
        { id: 217, name: '浦江县', height: 3000, esX: 119.903937, esY: 29.520086, color: [1, 56, 150, 0.9] },
        { id: 221, name: '开发区', height: 3000, esX: 119.63356, esY: 29.089116, color: [16, 92, 216, 0.9] },
      ],
      radioArr: ['底库人员', '重大事件', '执行评分'],
      area: '', //当前区域
      venuesList: [
        {
          name: '体育中心',
          code: 'Gfio1xWiA1E083LKKVAFUV',
          esX: 119.63847064547942,
          esY: 29.04367853193442,
          id: '101',
          address: '浙江省金华市双头龙南街2688号',
        },
        {
          name: '亚运分村',
          code: 'Gfio1xWiA1E083LKKVAFUV',
          esX: 119.68123346149268,
          esY: 29.081062703714288,
          id: '102',
          address: '浙江省金华市双头龙南街2688号',
        },
        {
          name: '浙师大体育馆',
          code: 'Gfio1xWiA1E083LKKVAFUV',
          esX: 119.63028688672114,
          esY: 29.138030058453477,
          id: '103',
          address: '浙江省金华市双头龙南街2688号',
        },
        {
          name: '万佛塔',
          code: 'Gfio1xWiA1E083LKKVAFUV',
          esX: 119.66364999938686,
          esY: 29.104795668374507,
          id: '104',
          address: '浙江省金华市双头龙南街2688号',
        },
        {
          name: '火炬起点',
          esX: 119.649808,
          esY: 29.09610129,
          address: '金华国际友城公园',
        },
        {
          name: '火炬终点',
          esX: 119.68326961095566,
          esY: 29.083440250135848,
          address: '金华亚运分村东门',
        },
        {
          name: '市本级应急点位',
          esX: 119.62511848362585,
          esY: 29.103237435903115,
          address: '金华亚运分村东门',
        },
        {
          name: '婺城区应急点位',
          esX: 119.65099380446327,
          esY: 29.139609045477073,
          address: '金华亚运分村东门',
        },
        {
          name: '金东区应急点位',
          esX: 119.6760129168193,
          esY: 29.093918415717205,
          address: '金华亚运分村东门',
        },
        {
          name: '开发区应急点位',
          esX: 119.6378301197201,
          esY: 29.028459786760138,
          address: '金华亚运分村东门',
        },
      ],
      //火炬流光线路
      hjdata: [
        [119.649808, 29.09610129],
        [119.64840671380419, 29.0950025353517],
        [119.64773244537406, 29.095456639761935],
        [119.64551153070208, 29.102774612572304],
        [119.64568682091964, 29.103929163897842],
        [119.64727097958433, 29.103250465990588],
        [119.6487182137916, 29.102876372141527],
        [119.64835103308428, 29.10119974445345],
        [119.65148357711631, 29.10123305133648],
        [119.65175398208503, 29.101309738699666],
        [119.65279181355699, 29.10189950024848],
        [119.65315062147464, 29.102323896017317],
        [119.65608327309769, 29.102410179127006],
        [119.65645796941917, 29.102368153559784],
        [119.65708929954438, 29.102193395744504],
        [119.65826083105047, 29.101989048084597],
        [119.66058938082456, 29.101999508692987],
        [119.6616385566243, 29.102164504724982],
        [119.66310212781292, 29.102897867413372],
        [119.66382967795639, 29.10207581325597],
        [119.66612983339596, 29.09989632237879],
        [119.66646932636313, 29.099130976517884],
        [119.66317818830505, 29.0977441277962],
        [119.66267572311295, 29.09739879577057],
        [119.66205323623454, 29.096886979775785],
        [119.66181172400553, 29.09656556875782],
        [119.66162277181827, 29.096023687992126],
        [119.66165288447934, 29.09537640957618],
        [119.66181105697197, 29.09505151496889],
        [119.66204697784507, 29.09466238363345],
        [119.66257718813044, 29.094190437200325],
        [119.66287905679181, 29.094022327594953],
        [119.66307988711115, 29.094159497376417],
        [119.66337142109094, 29.09429176673188],
        [119.66357619519319, 29.094343201371256],
        [119.66372957454107, 29.094342747716997],
        [119.66402607768373, 29.094138986044715],
        [119.66823590601179, 29.096214313509876],
        [119.67362222591369, 29.086063095536588],
        [119.67425059633655, 29.084398097071816],
        [119.67442404436945, 29.083241611296522],
        [119.6751924848931, 29.083361720219642],
        [119.67592512596877, 29.08389571056005],
        [119.67786362262817, 29.083056368696866],
        [119.68255888981889, 29.08481730266103],
        [119.68326961095566, 29.083440250135848],
      ],
      //火炬路线坐标
      hjlinespath: [
        {
          paths: [
            [
              [119.649808, 29.09610129],
              [119.64840671380419, 29.0950025353517],
              [119.64773244537406, 29.095456639761935],
              [119.64551153070208, 29.102774612572304],
              [119.64568682091964, 29.103929163897842],
              [119.64727097958433, 29.103250465990588],
              [119.6487182137916, 29.102876372141527],
              [119.64835103308428, 29.10119974445345],
              [119.65148357711631, 29.10123305133648],
              [119.65175398208503, 29.101309738699666],
              [119.65279181355699, 29.10189950024848],
              [119.65315062147464, 29.102323896017317],
              [119.65608327309769, 29.102410179127006],
              [119.65645796941917, 29.102368153559784],
              [119.65708929954438, 29.102193395744504],
              [119.65826083105047, 29.101989048084597],
              [119.66058938082456, 29.101999508692987],
              [119.6616385566243, 29.102164504724982],
              [119.66310212781292, 29.102897867413372],
              [119.66382967795639, 29.10207581325597],
              [119.66612983339596, 29.09989632237879],
              [119.66646932636313, 29.099130976517884],
              [119.66317818830505, 29.0977441277962],
              [119.66267572311295, 29.09739879577057],
              [119.66205323623454, 29.096886979775785],
              [119.66181172400553, 29.09656556875782],
              [119.66162277181827, 29.096023687992126],
              [119.66165288447934, 29.09537640957618],
              [119.66181105697197, 29.09505151496889],
              [119.66204697784507, 29.09466238363345],
              [119.66257718813044, 29.094190437200325],
              [119.66287905679181, 29.094022327594953],
              [119.66307988711115, 29.094159497376417],
              [119.66337142109094, 29.09429176673188],
              [119.66357619519319, 29.094343201371256],
              [119.66372957454107, 29.094342747716997],
              [119.66402607768373, 29.094138986044715],
              [119.66823590601179, 29.096214313509876],
              [119.67362222591369, 29.086063095536588],
              [119.67425059633655, 29.084398097071816],
              [119.67442404436945, 29.083241611296522],
              [119.6751924848931, 29.083361720219642],
              [119.67592512596877, 29.08389571056005],
              [119.67786362262817, 29.083056368696866],
              [119.68255888981889, 29.08481730266103],
              [119.68326961095566, 29.083440250135848],
            ],
          ],
        },
      ],
      camera: {
        position: {
          spatialReference: {
            latestWkid: 4490,
            wkid: 4490,
          },
          x: 120.0677977177787,
          y: 28.169461678562993,
          z: 205954.09350671433,
        },
        heading: 354.83632659832676,
        tilt: 25.420503545328554,
      },

      camera1: {
        position: {
          spatialReference: {
            latestWkid: 4490,
            wkid: 4490,
          },
          x: 120.08548183780617,
          y: 28.26180335575972,
          z: 169022.70734474808,
        },
        heading: 353.63677225554034,
        tilt: 28.33751217745334,
      },

      //视频相关
      title: '点位视频',
      spjkShow: false,
      afdwShow: false,
      hjShow: false,
      yycgShow: false,
      ws: DHWsInstance,
      videoDialogFlag: false,
      visible: false,
      videoConfig: {
        ctrlType: 'playerWin',
        ctrlCode: 'ctrl1',
        ctrlProperty: {
          displayMode: 1,
          splitNum: 1,
        },
        visible: true,
        domId: 'domt1',
      },
      code: '',
      createflag: false,
      setflag: false,
      destoryflag: false,
      info: {
        code: '33070251001311088752',
        value: 'GZ510225aQQ2婺城飘萍路婺州公园门口对面_A2020DX',
        orgName: '城中派出所',
      },
      //地图放大后的底部视频列表
      showVideoList: true,
      listData: [],
      codeList: [],
      videoConfigList: [
        {
          ctrlType: 'playerWin',
          ctrlCode: 'ctrl0',
          ctrlProperty: {
            displayMode: 1,
            splitNum: 1,
          },
          visible: true,
          domId: 'dom0',
        },
        {
          ctrlType: 'playerWin',
          ctrlCode: 'ctrl1',
          ctrlProperty: {
            displayMode: 1,
            splitNum: 1,
          },
          visible: true,
          domId: 'dom1',
        },
        {
          ctrlType: 'playerWin',
          ctrlCode: 'ctrl2',
          ctrlProperty: {
            displayMode: 1,
            splitNum: 1,
          },
          visible: true,
          domId: 'dom2',
        },
        {
          ctrlType: 'playerWin',
          ctrlCode: 'ctrl3',
          ctrlProperty: {
            displayMode: 1,
            splitNum: 1,
          },
          visible: true,
          domId: 'dom3',
        },
        {
          ctrlType: 'playerWin',
          ctrlCode: 'ctrl4',
          ctrlProperty: {
            displayMode: 1,
            splitNum: 1,
          },
          visible: true,
          domId: 'dom4',
        },
        {
          ctrlType: 'playerWin',
          ctrlCode: 'ctrl5',
          ctrlProperty: {
            displayMode: 1,
            splitNum: 1,
          },
          visible: true,
          domId: 'dom5',
        },
      ],
    }
  },
  components: {
    VenueVideo,
    CommonDialog,
  },
  props: {
    width: {
      type: String,
      default: '820px',
    },
    height: {
      type: String,
      default: '593px',
    },
    //展示对应label分值   0:重点人员label 1:重大事件label 2:执行评分label
    radioIndex: {
      type: Number,
      default: 2,
    },
    //是否展示业务功能例如左下角切换 视频监控 指令等等
    isShowFunction: {
      type: Boolean,
      default: true,
    },
    //是否展示label分值   1:只展示区划名称 2:都展示 0:都不展示 4:只展示分数
    noLabel: {
      type: Number,
      default: 2,
    },
    //是否根据接口level等级划定板块颜色
    isLayerColor: {
      type: Boolean,
      default: true,
    },
    //是否显示label
    isShowlabel: {
      type: Boolean,
      default: true,
    },
    //是否开启下钻
    isLoad: {
      type: Boolean,
      default: true,
    },
    //板块点击emit事件 1：radio+name 0:name
    type: {
      type: Number,
      default: 0,
    },
    //全屏状态 false:收起 true:展开(下沉)
    loadStatus: {
      type: Boolean,
      default: false,
    },
    //是否显示板块
    isShowLayer: {
      type: Boolean,
      default: true,
    },
    //当前页面
    currentPage: {
      type: Boolean,
      default: false,
    },
    // //是否显示板块
    // esriLayerLabel: {
    //   type: Boolean,
    //   default: false,
    // },
    //默认视角 0:展示板块外层视角 1:街道内层视角
    cameraType: {
      type: Number,
      default: 0,
    },
    page: {
      type: Boolean,
      default: false,
    },
    //是否开启全屏按钮
    showFullFuc: {
      type: Boolean,
      default: false,
    },
    //是否显示视频按钮
    isShowVideos: {
      type: Boolean,
      default: false,
    },
    isShowAfdw: {
      type: Boolean,
      default: false,
    },
    isShowLines: {
      type: Boolean,
      default: false,
    },
    isShowYycg: {
      type: Boolean,
      default: false,
    },
    isShowYjtz: {
      type: Boolean,
      default: false,
    },
    //是否显示LED点位
    isShowLEDPoints: {
      type: Boolean,
      default: false,
    },
  },
  created() {
    const container = document.getElementById('app')
    this.scaleWidth = 1920 / container.clientWidth
    this.scaleHeight = 1080 / container.clientHeight
    // 此函数放在生命周期中挂载阶段或其他
    this.init()
  },
  mounted() {
    window.nextStep = this.nextStep
    window.prevStep = this.prevStep
    getSceenVideo().then((res) => {
      console.log(res)
      this.newArr1 = res.data.data.map((item) => {
        return { children: item.list, label: item.name, id: item.id }
      })
    })
    getafdw().then((res) => {
      console.log(res)
      this.afdwData = res.data.data.map((item) => {
        return { children: item.list, label: item.label, id: item.id }
      })
    })
  },
  methods: {
    async init() {
      await this.getRegionStatistics(this.radioIndex)
      await this.initMap()
    },
    //----------地图相关------------
    //初始化地图
    async initMap() {
      const view = window.ArcGisUtils.initSceneView({
        //   divId: 'viewDiv',
        //   basemap: {
        //     title: 'BLUEGrey0830',
        //     layerConfigs: {
        //       type: 'tile',
        //       id: 'ebdd252e943146439309da101a87a1f7',
        //       url: 'https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/ditu0830/MapServer',
        //       // url: 'https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/ditu0830_16_9/MapServer',
        //     },
        //   },
        //   viewingModeExtend: 'global',
        //   isDefaultGoToFromGlobal: false,
        //   // camera: this.cameraType == 0 ? this.camera : this.camera1,
        //   camera: this.camera,
        // })
        divId: 'viewDiv',
        camera:
          this.cameraType == 1
            ? {
                position: {
                  spatialReference: {
                    wkid: 4490,
                  },
                  x: 119.67217970183873,
                  y: 28.981481618287248,
                  z: 10280.48295974452,
                },
                heading: 354.2764876066964,
                tilt: 47.90938191064288,
              }
            : this.camera1,
        basemap: 'TDT_img', //vector
      })
      view.zoom = this.page == true ? 12.2 : 8.5
      view.when(() => {
        // view.qualityProfile="high"
        if (this.deptId != 202) {
          window.view.goTo(this.getLayerCenter(this.mapList[0]))
        }
        // this.draw()
        // this.loadImportantVenuesPoint()
        // this.loadStreet()
        if (this.isShowLEDPoints) {
          this.loadLEDPoints()
        }
        if (this.isLayerColor) {
          this.getLayerColor(this.mapList)
        }
        // this.loadRegionLayer(this.mapList)
        if (this.isShowlabel) {
          this.loadRegionLabel(this.mapList)
        }
        // 监听地图放大缩小控制是否显示板块和label
        window.view.watch('zoom', (zindex) => {
          if (window.arealayer) {
            if (zindex > 11) {
              window.arealayer.visible = false
            }
            if (zindex < 11) {
              window.arealayer.visible = true
            }
          }
        })
      })
    },
    checkChange(node, flag, arr) {
      if (node.ids) {
        request2(`/jazz-api/instruction/video/getVideoByIds/${node.ids}`, 'get').then((res) => {
          console.log(res)
          var newArr = res.data.data.map((item) => {
            return {
              key: item.dkey,
              value: item.dvalue,
              esX: item.esX,
              esY: item.esY,
              orgname: item.orgname,
              orgName: item.orgName,
            }
          })
          if (newArr.length == 1) {
            if (flag) {
              this.loadPoint(newArr, node.id, 30, 'video333', 1)
            } else {
              top.view.map.remove(window['layer' + node.id])
            }
          } else {
            if (flag) {
              this.getManyPoint(newArr, node.id)
            } else {
              mapUtil.removeLayer(`juhetest${node.id}`)
            }
          }
          mapUtil.flyTo({
            destination: [newArr[0].lng, newArr[0].lat],
          })
        })
      }
    },
    checkChange1(node, flag, arr) {
      console.log(node)
      if (flag) {
        if (node.label == '亚运场馆') {
          this.loadPoint([this.venuesList[0]], 50, 100, 'tyzx')
          this.loadPoint([this.venuesList[1]], 51, 100, 'yyfc')
          this.loadPoint([this.venuesList[2]], 52, 100, 'zsdtyg')
          this.loadPoint([this.venuesList[3]], 53, 100, 'wft')
        } else {
          if (node.label == '婺城区') {
            this.loadPoint([this.venuesList[7]], 56, 40, '应急')
          }
          if (node.label == '市本级') {
            this.loadPoint([this.venuesList[6]], 57, 40, '应急')
          }
          if (node.label == '金东区') {
            this.loadPoint([this.venuesList[8]], 58, 40, '应急')
          }
          if (node.label == '开发区') {
            this.loadPoint([this.venuesList[9]], 59, 40, '应急')
          }
        }
      } else {
        if (node.label == '亚运场馆') {
          window.view.map.remove(window.layer50)
          window.view.map.remove(window.layer51)
          window.view.map.remove(window.layer52)
          window.view.map.remove(window.layer53)
        }
        if (node.label == '市本级') {
          window.view.map.remove(window.layer57)
        }
        if (node.label == '婺城区') {
          window.view.map.remove(window.layer56)
        }
        if (node.label == '金东区') {
          window.view.map.remove(window.layer58)
        }
        if (node.label == '开发区') {
          window.view.map.remove(window.layer59)
        }
      }
    },
    afdwChange(node, flag, arr) {
      if (node.label != '已检查' && node.label != '未检查')
        request2(`/jazz-api/bigScreen/gkPerson/get_mcaf_by_category?category=${node.label}`, 'get').then((res) => {
          if (res.data.data != undefined) {
            var newArr = res.data.data.map((item) => {
              return {
                id: item.id,
                esX: item.lon,
                esY: item.lat,
                companyAddress: item.companyAddress,
                Category: item.companyCategory,
                companyCategory: item.companyCategory,
                companyName: item.companyName,
                issueCount: item.issueCount,
                resolvedCount: item.resolvedCount,
                missionStartTime: item.missionStartTime,
              }
            })
            var picture = this.typeList.indexOf(node.label) >= 0 ? node.label : '通用点位'
            if (newArr.length == 1) {
              if (flag) {
                this.loadPoint1(newArr, node.id, 30, picture, 1)
              } else {
                top.view.map.remove(window['layer' + node.id])
              }
            } else {
              if (flag) {
                this.getManyPoint1(newArr, node.id, picture)
              } else {
                mapUtil.removeLayer(`juhetest${node.id}`)
              }
            }
            mapUtil.flyTo({
              destination: [newArr[0].lng, newArr[0].lat],
            })
          }
        })
    },

    //加载板块label文字
    loadRegionLabel(data) {
      // this.removeLabel()
      const layerid = 'jazz-label'
      //使用map将数据替换为指定格式的对象数组
      let list = data.map((item, idx) => {
        return {
          id: layerid + idx,
          position: [item.esX, item.esY, 0],
          properties: item,
          content: this.getContent(item),
          offset: [0, 0],
        }
      })
      const _onclick = function (e) {
        console.log(e)
      }
      window.esriLayerLabel = window.gisHelper.createCustomLayer({
        view: window.view,
        layerid,
        data: list,
        style: { size: [200, 200], offset: [0, 25] },
        onclick: _onclick,
      })
    },
    //板块加载
    loadRegionLayer(data, down) {
      if (this.isShowLayer) {
        this.removeLayer()
        let quhuajson = { ...this.quhuajson }
        //下钻处理
        if (down) {
          quhuajson.features = quhuajson.features.filter((item) => item.properties.name == data[0].name)
        } else {
          //将每个区划都赋上板块高度和颜色
          quhuajson.features.forEach((item) => {
            data.forEach((obj) => {
              if (obj.name == item.properties.name) {
                item.properties.height = obj.height
                item.properties.color = obj.color
              }
            })
          })
          //过滤出已经赋上了高度/颜色的元素(因为传进来的data可能只是部分区划 如果不进行过滤剩余区划不包含height或color字段会报错)
          quhuajson.features = quhuajson.features.filter((item) => item.properties.height && item.properties.color)
        }
        window.arealayer = window.ArcGisUtils.addExtrudeLayer({
          view: window.view,
          geojson: quhuajson,
          colorField: 'color',
          sizeField: 'height',
          onClick: (data) => {
            this.labelClick(data.name)
            //如果是点位点击不触发下钻
            if (!this.spjkShow && this.isLoad && window.view.zoom < 11) {
              // this.getLayerCamera(data)
              this.getLayerCamera2(data)
            }
          },
        })
      }
    },
    //打点并设置弹框
    async loadPoint(points, i, size, url, Pop) {
      const that = this
      window['layer' + i] = await window.ArcGisUtils.loadArcgisLayer(window.view, {
        code: 1, // code的类型
        title: '', // 图层名字
        type: 'customFeature', // 1,3,5 传入这个类型
        objectIdField: 'id', // 接口返回值：唯一的字段
        rendererIcon: {
          size: size, // 图片大小
          src: require(`@/assets/img/map/${url}.png`), // 图片src
        },
        data: points,
      })
      window['layer' + i].elevationInfo = {
        mode: 'relative-to-scene',
        offset: 0,
        unit: 'meters',
      }
      if (i == 55) {
        window.view.goTo({
          position: {
            spatialReference: {
              wkid: 4490,
            },
            x: 119.67176141584929,
            y: 29.04226363073756,
            z: 4358.808461298235,
          },
          heading: 354.2775695093465,
          tilt: 47.96829969487565,
        })
      } else {
        //切换地图视角
        window.view.goTo({
          position: {
            spatialReference: {
              wkid: 4490,
            },
            x: points[0].esX,
            y: points[0].esY - 0.13,
            z: 10280.48295974452,
          },
          heading: 354.28314794417963,
          tilt: 47.90938191064288,
        })
      }
      //判断是否要弹框
      if (Pop) {
        window.ArcGisUtils.mapClickEventHandle.add(window['layer' + i].id, (point, graphic) => {
          if (graphic) {
            // 点击到图形
            const { attributes } = graphic
            console.log(attributes, 'PointClick!!!')
            //让pointList长度为1
            this.pointList = [1]
            that.getPointInfo(attributes)
          }
        })
      }
    },
    //打点并设置弹框
    async loadPoint1(points, i, size, url, Pop) {
      const that = this
      window['layer' + i] = await window.ArcGisUtils.loadArcgisLayer(window.view, {
        code: 1, // code的类型
        title: '', // 图层名字
        type: 'customFeature', // 1,3,5 传入这个类型
        objectIdField: 'id', // 接口返回值：唯一的字段
        rendererIcon: {
          size: size, // 图片大小
          src: require(`@/assets/img/map/${url}.png`), // 图片src
        },
        data: points,
      })
      window['layer' + i].elevationInfo = {
        mode: 'relative-to-scene',
        offset: 0,
        unit: 'meters',
      }
      if (i == 55) {
        window.view.goTo({
          position: {
            spatialReference: {
              wkid: 4490,
            },
            x: 119.67176141584929,
            y: 29.04226363073756,
            z: 4358.808461298235,
          },
          heading: 354.2775695093465,
          tilt: 47.96829969487565,
        })
      } else {
        //切换地图视角
        window.view.goTo({
          position: {
            spatialReference: {
              wkid: 4490,
            },
            x: points[0].esX,
            y: points[0].esY - 0.13,
            z: 10280.48295974452,
          },
          heading: 354.28314794417963,
          tilt: 47.90938191064288,
        })
      }
      //判断是否要弹框
      if (Pop) {
        window.ArcGisUtils.mapClickEventHandle.add(window['layer' + i].id, (point, graphic) => {
          if (graphic) {
            // 点击到图形
            const { attributes } = graphic
            console.log(attributes, 'PointClick!!!')
            //让pointList长度为1
            this.pointList = [1]
            that.afdwPop(attributes)
          }
        })
      }
    },
    //加载道路服务
    // async loadStreet() {
    //   let streetlayer = window.ArcGisUtils.loadArcgisLayer(window.view, {
    //     type: 'tile',
    //     id: 'bc13aa68b2a34be799337947ce5b9e21',
    //     renderer: {
    //       type: 'line',
    //       symbol: {
    //         type: 'line', // autocasts as new SimpleMarkerSymbol()
    //         color: [226, 119, 40, 0],
    //         size: 0,
    //       },
    //     },
    //     labelingInfo: [
    //       {
    //         labelExpressionInfo: {expression: '$feature.FNAME'},
    //         symbol: {
    //           type: 'text', // autocasts as new TextSymbol()
    //           color: [54, 136, 180],
    //           haloSize: 0,
    //           haloColor: 'white',
    //           font: {
    //             size: 10,
    //             weight: 'normal',
    //           },
    //         },
    //       },
    //     ],
    //     url: 'https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/road_label_16_9_0830/MapServer',
    //   }).then((res) => {
    //     window.streetlayer = res
    //     window.streetlayer.visible = false
    //   })
    //   window.view.watch('zoom', (zindex) => {
    //     if (window.streetlayer) {
    //       if (zindex > 12.5) {
    //         window.streetlayer.visible = true
    //       }
    //       if (zindex < 12.5) {
    //         window.streetlayer.visible = false
    //       }
    //     }
    //   })
    // },
    //加载3w点位
    async loadLines() {
      const that = this
      window.hjlayer = window.ArcGisUtils.addLineLayer({
        width: 5,
        color: '#00C47C',
        data: this.hjlinespath,
      })
      window.hjlayer.elevationInfo = {
        mode: 'on-the-ground',
        offset: 0,
        unit: 'meters',
      }
    },
    async loadLEDPoints() {
      window.LEDPointslayer = window.ArcGisUtils.loadArcgisLayer(window.view, {
        type: 'feature',
        id: 'a61b953c9862400abcdb6556dca41afd',
        renderer: {
          type: 'simple',
          symbol: {
            type: 'simple-marker', // autocasts as new SimpleMarkerSymbol()
            color: [54, 136, 180, 10],
            size: 10,
          },
        },
        url: 'https://csdnwlgz.dsjj.jinhua.gov.cn/server/rest/services/Hosted/LED%E5%A4%A7%E5%B1%8F/FeatureServer',
      }).then((ledlayer) => {
        window.LEDPointslayer = ledlayer
        window.LEDPointslayer.visible = false
        //下面这两行代码就是可以让服务中自带的弹框显示出来
        // top.view.popup.autoOpenEnabled = true
        // top.view.popup.defaultPopupTemplateEnabled = true
      })
      window.view.watch('zoom', (zindex) => {
        if (window.LEDPointslayer) {
          if (zindex < 10.5) {
            LEDPointslayer.visible = false
          } else {
            LEDPointslayer.visible = true
          }
        }
      })
    },
    //清除label
    removeLabel() {
      if (window.esriLayerLabel) {
        window.esriLayerLabel.remove()
      }
    },
    //清除板块
    removeLayer() {
      if (window.arealayer) {
        window.view.map.remove(window.arealayer)
      }
    },
    //清除打点
    removePointLayer() {
      if (window.layer) window.view.map.remove(window.layer)
    },
    //返回上一级
    goTop() {
      this.area = ''
      if (this.deptId != 202) {
        window.view.goTo(this.getLayerCenter(this.mapList[0]))
      } else {
        window.view.goTo({
          position: {
            spatialReference: {
              wkid: 4490,
            },
            x: 120.01978859863378,
            y: 28.228380508652613,
            z: 177768.853501250036,
          },
          heading: 358.6029680851125,
          tilt: 26.40692685073874,
        })
      }
      this.getLayerColor(this.mapList)
      this.loadRegionLayer(this.mapList)
      this.loadRegionLabel(this.mapList)
    },

    //画圈
    async draw() {
      try {
        if (!window?.drawTool) {
          window.drawTool = new window.ArcGisUtils.Draw({ view: window.view })
        }
        await window.drawTool.draw('circle').then((res) => {
          console.log(res)
          this.draw()
        })
      } catch (e) {
        console.log(e)
      }
    },
    clearOnClick() {
      // 调用方法clear()清除绘制
      window.drawTool.clear()
    },
    destroyOnClick() {
      if (window.drawTool) {
        window.drawTool.destroy()
        window.drawTool = null
      }
    },

    //----------业务相关------------
    //跳转指令
    goDirective() {
      console.log('跳转后台指令')
      location.href = process.env.VUE_APP_ADMIN_URL + '/direct/direction'
    },
    //左下角选择框点击
    clickRadio(i) {
      this.radioIndex = i
      this.getRegionStatistics(i, 'isChange')
    },
    // 查询板块label分数/事件/人员 type:0重点人员 type:1重大事件 type:2执行评分
    getRegionStatistics(type, change) {
      getRegionStatistics({ type: type })
        .then((res) => {
          //给mapList每个区划赋值count(分值)level(等级)
          res.data.data.forEach((item, i) => {
            this.mapList.find((item2) => {
              if (item.dutyPlace == item2.name) {
                item2.count = item.count
                item2.level = item.level
              }
            })
          })
        })
        .then((res) => {
          //id为202展示全金华 不为202过滤出对应单独展示的板块数据
          if (this.deptId != 202) {
            this.mapList = this.mapList.filter((item) => item.id == this.deptId)
          }
          //如果是切换时调用 重新加载板块与label 不加change参数判断的话初始化已经调用过加载板块与label了再调一次就重复调用了
          if (change) {
            this.getLayerColor(this.mapList)
            this.loadRegionLayer(this.mapList)
            this.loadRegionLabel(this.mapList)
          }
        })
    },
    //点击图层显示弹框
    labelClick(name) {
      console.log(name, 'labelClick')
      if (this.type == 0) {
        this.$emit('showMapDetails', name)
      } else if (this.type == 1) {
        this.$emit('showMapDetails', this.radioIndex, name)
      }
    },
    //暗访督查点位弹窗
    afdwPop(e) {
      console.log(e)
      let this_ = this
      let str = `<div style="position: relative;background-size: 100% 100%;width: max-content;min-height: 250px;">
            <header class="s-m-l-20 s-m-r-20 s-m-t-10 s-flex s-row-between s-font-30" >
                <div class="s-m-l-40">
                  <p style="width: 290px;font-size: 15px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">名称 ：<span style="color: #ffff;">${
                    e.companyName
                  }</span></p>
                  <p style="margin-top:10px; width: 290px;font-size: 15px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">地址 ：<span style="color: #ffff;white-space: normal">${
                    e.companyAddress
                  }</span></p>
                    <p style="margin-top:10px; width: 290px;font-size: 15px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">单位类型 ：<span style="color: #ffff;">${
                      e.Category
                    }</span></p>
                    <p style="margin-top:10px; width: 290px;font-size: 15px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">暗访时间 ：<span style="color: #ffff;">${
                      e.missionStartTime
                    }</span></p>
                      <p style="margin-top:10px; width: 290px;font-size: 15px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">发现问题 ：<span style="color: #ffff;">${
                        e.issueCount
                      } 个</span></p>
                      <p style="margin-top:10px; width: 290px;font-size: 15px; color: #2299e2;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">已整改 ：<span style="color: #ffff;">${
                        e.resolvedCount
                      } 个</span></p>
                </div>
                <div class="jiajian1">
                  <img
                    src=${this.urlR}
                    alt=""
                    height="50"
                    width="50"
                    v-show="titleListShow"
                    :class="leftDisabled?'disPoint':'point'"
                    onclick="prevStep()"
                  />
                  <div>
                    <span v-show="titleListShow" style="line-height:45px; color: #00c0ff; font-size: 20px"
                    >${this.pointIndex + 1}/${this.pointList.length}</span
                    >
                  </div>
                  <img
                    src=${this.urlL}
                    alt=""
                    height="50"
                    width="50"
                    v-show="titleListShow"
                    :class="rightDisabled?'disPoint':'point'"
                    onclick="nextStep()"
                  />
                </div>
            </header>
          </div> `
      let objData = {
        layerid: 'afdw_pop',
        position: [e.esX, e.esY],
        offset: [-550, -100],
        content: str,
        title: `${e.Category}点位详情`,
        backurl: this_.backurl,
      }
      mapUtil._createPopup(objData)
    },
    //获取level对应的content
    getContent(item) {
      let unit = this.getUnit()
      let content = ''
      let content1 = `
              <div onclick="labelClick('${item.name}')">
                <div style="display: flex;flex-direction: column;align-content: center;align-items: center;justify-content: center;">
                  <div style="display: flex;justify-content: center;align-items: flex-end;align-content: flex-end;font-size: 32px;font-weight:600;background: linear-gradient(180deg, #FFFFFF 0%, #FF6D6D 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">${item.count} <span style="font-size: 14px;margin: 0 0 10px 7px">${unit}</span> </div>
                  <div style="font-size:16px;color: #fff; font-family: Source Han Sans CN; font-weight:bold;line-height:25px; text-shadow: #000 1px 0 0, #000 0 1px 0, #000 -1px 0 0, #000 0 -1px 0;cursor:default">${item.name}</div>
                </div>
              </div>
               `
      let content2 = `
              <div onclick="labelClick('${item.name}')">
                <div style="display: flex;flex-direction: column;align-content: center;align-items: center;justify-content: center;">
                  <div style="display: flex;justify-content: center;align-items: flex-end;align-content: flex-end;font-size: 32px;font-weight:600;background: linear-gradient(180deg, #FFFFFF 0%, #33BEFF 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">${item.count} <span style="font-size: 14px;margin: 0 0 10px 7px">${unit}</span> </div>
                  <div style="font-size:16px;color: #fff; font-family: Source Han Sans CN; font-weight:bold;line-height:25px; text-shadow: #000 1px 0 0, #000 0 1px 0, #000 -1px 0 0, #000 0 -1px 0;cursor:default">${item.name}</div>
                </div>
              </div>
               `
      let content3 = `
              <div onclick="labelClick('${item.name}')">
                <div style="display: flex;flex-direction: column;align-content: center;align-items: center;justify-content: center;">
                  <div style="display: flex;justify-content: center;align-items: flex-end;align-content: flex-end;font-size: 32px;font-weight:600;background: linear-gradient(180deg, #FFFFFF 0%, #F5CC53 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">${item.count} <span style="font-size: 14px;margin: 0 0 10px 7px">${unit}</span> </div>
                  <div style="font-size:16px;color: #fff; font-family: Source Han Sans CN; font-weight:bold;line-height:25px; text-shadow: #000 1px 0 0, #000 0 1px 0, #000 -1px 0 0, #000 0 -1px 0;cursor:default">${item.name}</div>
                </div>
              </div>
               `
      let content4 = `
              <div onclick="labelClick('${item.name}')">
                <div style="display: flex;flex-direction: column;align-content: center;align-items: center;justify-content: center;">
                  <div style="font-size:16px;color: #fff; font-family: Source Han Sans CN; font-weight:bold;line-height:25px; text-shadow: #000 1px 0 0, #000 0 1px 0, #000 -1px 0 0, #000 0 -1px 0;cursor:default">${item.name}</div>
                </div>
              </div>
               `

      let content5 = `
              <div onclick="labelClick('${item.name}')">
                <div style="display: flex;flex-direction: column;align-content: center;align-items: center;justify-content: center;">
                  <div style="display: flex;justify-content: center;align-items: flex-end;align-content: flex-end;font-size: 32px;font-weight:600;background: linear-gradient(180deg, #FFFFFF 0%, #FF6D6D 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">${item.count} <span style="font-size: 14px;margin: 0 0 10px 7px">${unit}</span> </div>
                </div>
              </div>
               `
      let content6 = `
              <div onclick="labelClick('${item.name}')">
                <div style="display: flex;flex-direction: column;align-content: center;align-items: center;justify-content: center;">
                  <div style="display: flex;justify-content: center;align-items: flex-end;align-content: flex-end;font-size: 32px;font-weight:600;background: linear-gradient(180deg, #FFFFFF 0%, #33BEFF 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">${item.count} <span style="font-size: 14px;margin: 0 0 10px 7px">${unit}</span> </div>
                </div>
              </div>
               `
      let content7 = `
              <div onclick="labelClick('${item.name}')">
                <div style="display: flex;flex-direction: column;align-content: center;align-items: center;justify-content: center;">
                  <div style="display: flex;justify-content: center;align-items: flex-end;align-content: flex-end;font-size: 32px;font-weight:600;background: linear-gradient(180deg, #FFFFFF 0%, #F5CC53 100%);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">${item.count} <span style="font-size: 14px;margin: 0 0 10px 7px">${unit}</span> </div>
                </div>
              </div>
               `
      if (item.count == null || this.noLabel == 1) {
        content = content4
      } else if (item.count == null || this.noLabel == 0) {
        content = ''
      } else if (item.count == null || this.noLabel == 4) {
        switch (item.level) {
          case 1:
            content = content5
            break
          case 2:
            content = content6
            break
          case 3:
            content = content7
            break
        }
      } else if (item.count == null || this.noLabel == 2) {
        switch (item.level) {
          case 1:
            content = content1
            break
          case 2:
            content = content2
            break
          case 3:
            content = content3
            break
        }
      }
      return content
    },
    //设置板块对应颜色
    getLayerColor(data) {
      if (this.isLayerColor) {
        //给mapList中每个区划根据level等级赋值板块颜色 color字段
        data.forEach((item, i) => {
          if (item.level == 1) {
            item.color = [109, 240, 207, 0.7]
          } else if (item.level == 2) {
            item.color = [66, 189, 255, 0.7]
          } else if (item.level == 3) {
            item.color = [245, 208, 117, 0.7]
          } else {
            item.color = [66, 189, 255, 0.7]
          }
        })
      } else {
        //给mapList中每个区划设置默认板块颜色 color字段
        data.forEach((item, i) => {
          item.color = [66, 189, 255, 0.7]
        })
      }
    },
    //各县市区板块下沉
    getLayerCamera(data) {
      // this.removeLabel()
      this.area = data.name
      this.getLayerColor([data])
      this.loadRegionLayer([data], true)
      let camera = {}
      if (data.name == '婺城区') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 119.517731193196,
            y: 28.61224516757242,
            z: 71237.47615350317,
          },
          heading: 358.5933751113065,
          tilt: 27.173586095498596,
        }
      } else if (data.name == '义乌市') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 120.05807271308194,
            y: 28.93928089313697,
            z: 69323.51278573181,
          },
          heading: 358.5913669650196,
          tilt: 27.198242523061868,
        }
      } else if (data.name == '金东区') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 119.8299745327611,
            y: 28.842147713980264,
            z: 69323.51278573088,
          },
          heading: 358.5930706876838,
          tilt: 27.199907125899486,
        }
      } else if (data.name == '东阳市') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 120.3989433291354,
            y: 28.877714649520833,
            z: 69323.51278572902,
          },
          heading: 358.5883305101098,
          tilt: 27.19535225525377,
        }
      } else if (data.name == '永康市') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 120.14414742371314,
            y: 28.595848926171726,
            z: 69323.51278572809,
          },
          heading: 358.5771104594613,
          tilt: 27.20171645518499,
        }
      } else if (data.name == '兰溪市') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 119.54275485411712,
            y: 28.930806466335213,
            z: 69323.51278572995,
          },
          heading: 358.5796338643893,
          tilt: 27.202318762038797,
        }
      } else if (data.name == '武义县') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 119.72392908332155,
            y: 28.449981686063957,
            z: 94753.05925309844,
          },
          heading: 354.3920325932369,
          tilt: 19.809697711861784,
        }
      } else if (data.name == '磐安县') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 120.54726755562434,
            y: 28.716330519477125,
            z: 69340.09491234832,
          },
          heading: 358.5911782827425,
          tilt: 27.195712501796685,
        }
      } else if (data.name == '浦江县') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 119.93794622383083,
            y: 29.217984575538424,
            z: 69323.51278572995,
          },
          heading: 358.59213922779253,
          tilt: 27.19964767627428,
        }
      } else if (data.name == '开发区') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 119.49577633089044,
            y: 29.007788599590864,
            z: 94770.80276363809,
          },
          heading: 343.4005895148645,
          tilt: 2.093204347546646,
        }
      }
      window.view.goTo(camera)
    },
    //各县市区板块下沉到市政府
    getLayerCamera2(data) {
      console.log(data)
      this.area = data.name
      let camera = {}
      if (data.name == '婺城区') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 119.5675435053431,
            y: 29.087666660583693,
            z: 388.1785184908658,
          },
          heading: 358.59363853376965,
          tilt: 27.19983227440402,
        }
      } else if (data.name == '义乌市') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 120.07117186973488,
            y: 29.305924175305194,
            z: 579.4799508964643,
          },
          heading: 358.5913669650198,
          tilt: 27.198242523061893,
        }
      } else if (data.name == '金东区') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 119.68859542226718,
            y: 29.100577488042116,
            z: 394.46445204131305,
          },
          heading: 358.5930706876841,
          tilt: 27.199907125899486,
        }
      } else if (data.name == '东阳市') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 120.23799876741779,
            y: 29.289994776317247,
            z: 567.7934789694846,
          },
          heading: 358.58833051011186,
          tilt: 27.19535225525401,
        }
      } else if (data.name == '永康市') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 120.04327076511713,
            y: 28.88963652937494,
            z: 527.8300804467872,
          },
          heading: 358.57711045946314,
          tilt: 27.201716455185007,
        }
      } else if (data.name == '兰溪市') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 119.45576854525716,
            y: 29.209786565034676,
            z: 282.0917588835582,
          },
          heading: 358.5796338643901,
          tilt: 27.202318762038807,
        }
      } else if (data.name == '武义县') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 119.81240744173114,
            y: 28.893612440209033,
            z: 441.7036886299029,
          },
          heading: 358.5931967429324,
          tilt: 27.19969005819363,
        }
      } else if (data.name == '磐安县') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 120.44599973486056,
            y: 29.05569528753912,
            z: 584.666400551796,
          },
          heading: 358.59115455784683,
          tilt: 27.195866973589787,
        }
      } else if (data.name == '浦江县') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 119.88811238355196,
            y: 29.45367479346751,
            z: 388.3486876273528,
          },
          heading: 358.59213922779355,
          tilt: 27.199647676274143,
        }
      } else if (data.name == '开发区') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 119.63356034438651,
            y: 29.08911612476775,
            z: 331.95223069097847,
          },
          heading: 358.5927625391668,
          tilt: 27.200129299180265,
        }
      }
      window.view.goTo(camera)
    },
    //获取各县市区中心点
    getLayerCenter(data) {
      console.log(data)
      let camera = {}
      if (data.name == '婺城区') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 119.54822258136898,
            y: 28.439144409532233,
            z: 116008.85604015458,
          },
          heading: 358.60069294123815,
          tilt: 26.676432339933058,
        }
      } else if (data.name == '义乌市') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 120.08703761502251,
            y: 28.686818854207676,
            z: 128557.70136456098,
          },
          heading: 358.599384338125,
          tilt: 26.620817661673286,
        }
      } else if (data.name == '金东区') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 119.8457087711578,
            y: 28.56940451286557,
            z: 128357.5683118673,
          },
          heading: 358.6008549868031,
          tilt: 26.622343662270044,
        }
      } else if (data.name == '东阳市') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 120.41592165952548,
            y: 28.54652534351939,
            z: 151413.13807610888,
          },
          heading: 358.5975430776517,
          tilt: 26.517242423364646,
        }
      } else if (data.name == '永康市') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 120.14540583653634,
            y: 28.3205145507481,
            z: 135915.11470966693,
          },
          heading: 358.58534668436425,
          tilt: 26.591650088225748,
        }
      } else if (data.name == '兰溪市') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 119.55270574315894,
            y: 28.79674929559755,
            z: 99739.34098950308,
          },
          heading: 358.5858163026108,
          tilt: 26.751086464368896,
        }
      } else if (data.name == '武义县') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 119.73403818586314,
            y: 28.09401205620158,
            z: 147741.37067047507,
          },
          heading: 358.60203484650293,
          tilt: 26.53667886661869,
        }
      } else if (data.name == '磐安县') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 120.56904479004865,
            y: 28.559628948892886,
            z: 102970.14745222684,
          },
          heading: 358.59741283557065,
          tilt: 26.731735837514982,
        }
      } else if (data.name == '浦江县') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 119.95826319596326,
            y: 28.944494075293438,
            z: 130795.7373212399,
          },
          heading: 358.59454963622295,
          tilt: 26.60075714957501,
        }
      } else if (data.name == '开发区') {
        camera = {
          position: {
            spatialReference: {
              latestWkid: 4490,
              wkid: 4490,
            },
            x: 119.54822258136898,
            y: 28.439144409532233,
            z: 116008.85604015458,
          },
          heading: 358.60069294123815,
          tilt: 26.676432339933058,
        }
      }
      return camera
    },
    //获取单位
    getUnit() {
      let unit = ''
      switch (this.radioIndex) {
        case 0:
          unit = '人'
          break
        case 1:
          unit = '件'
          break
        case 2:
          unit = '分'
          break
      }
      return unit
    },
    //全屏
    fullMap(type) {
      this.logoutVideo()
      this.listData = []
      this.showVideoList = true
      this.$emit('full', type)
    },

    //----------视频相关------------
    //监控点位打点聚合
    onclick(e, list) {
      console.log(e, list)
      if (e.status == 0) {
        this.$message('设备离线')
        return
      }
      mapUtil.flyTo({
        destination: [e.esX, e.esY],
        // zoom: 15,
        offset: [0, -666],
      })
      this.pointList = list
      console.log(this.currentPage)
      if (this.currentPage) {
        this.afdwPop(e)
      } else {
        this.getPointInfo(e)
      }
    },
    prevStep() {
      this.pointIndex--
      // console.log(this.pointIndex);
      if (this.pointIndex < 0) {
        this.leftDisabled = true
        this.pointIndex = 0
        return
      } else if (this.pointIndex == 0) {
        this.leftDisabled = true
        this.rightDisabled = false
        this.pointIndex = 0
      } else {
        this.rightDisabled = false
      }
      if (this.currentPage) {
        this.afdwPop(this.pointList[this.pointIndex])
      } else {
        this.getPointInfo(this.pointList[this.pointIndex])
      }
    },
    nextStep() {
      this.pointIndex++
      // console.log(this.pointIndex);
      if (this.pointIndex > this.pointList.length - 1) {
        this.rightDisabled = true
        this.pointIndex = this.pointList.length - 1
        return
      } else if (this.pointIndex == this.pointList.length - 1) {
        this.rightDisabled = true
        this.leftDisabled = false
        this.pointIndex = this.pointList.length - 1
      } else {
        this.leftDisabled = false
      }
      if (this.currentPage) {
        this.afdwPop(this.pointList[this.pointIndex])
      } else {
        this.getPointInfo(this.pointList[this.pointIndex])
      }
    },
    getManyPoint(pointData, pointId) {
      debugger
      mapUtil.loadPointLayer({
        layerid: `juhetest${pointId}`,
        data: pointData,
        onclick: this.onclick,
        onblur: this.onblur,
        cluster: true, //是否定义为聚合点位：true/false
        iconcfg: {
          image: require(`@/assets/img/map/video333.png`),
          iconSize: 30,
          iconlist: {
            field: 'orgName',
            list: [
              {
                value: '',
                size: '30',
                src: require(`@/assets/img/map/video333.png`),
              },
            ],
          },
        },
      })
    },
    getManyPoint1(pointData, pointId, label) {
      console.log(pointData, label)
      mapUtil.loadPointLayer({
        layerid: `juhetest${pointId}`,
        data: pointData,
        onclick: this.onclick,
        onblur: this.onblur,
        cluster: true, //是否定义为聚合点位：true/false
        iconcfg: {
          image: require(`@/assets/img/map/${label}.png`),
          iconSize: 30,
          iconlist: {
            field: 'companyCategory',
            list: [
              {
                value: label,
                size: '30',
                src: require(`@/assets/img/map/${label}.png`),
              },
            ],
          },
        },
      })
    },
    // 视频监控点击
    spjkClick() {
      this.spjkShow = !this.spjkShow
    },
    //暗访点位点击
    afdwClick() {
      this.afdwShow = !this.afdwShow
    },
    //火炬点击
    hjClick() {
      this.hjShow = !this.hjShow
    },
    yycgClick() {
      this.yycgShow = !this.yycgShow
    },
    yjtzClick() {
      this.$emit('zhddClick')
    },
    //打开视频
    showVenueVideo() {
      this.newVenueVideo()
      this.visible = true
    },
    //关闭视频
    closeVideo() {
      this.visible = false
    },
    //视频初始化
    newVenueVideo() {
      this.logoutVideo()
      this.getMinitorData()
    },
    //退出视频
    logoutVideo() {
      this.allVideoDestory()
      this.logout()
    },
    //视频配置相关
    getMinitorData() {
      if (this.code !== '') {
        if (this.isLogin) {
          this.setAllvideo()
        } else {
          this.loginVideo()
        }
      } else {
        this.code = ''
        if (this.isLogin) {
          this.setAllvideo()
        }
      }
      // });
    },
    //视频登陆
    loginVideo() {
      // let DHWsInstance = this.ws;
      const _this = this
      this.ws.detectConnectQt().then((res) => {
        if (res) {
          // 连接客户端成功
          this.ws.login({
            loginIp: '*************',
            loginPort: '8281',
            userName: 'jazzdj',
            userPwd: 'jangD1zoS6^q',
            token: '',
          })
          this.ws.on('loginState', (res) => {
            if (res) {
              console.log('登录成功')
              _this.createAllvideo()
              _this.setAllvideo()
              this.isLogin = true
            } else {
              console.log('登录失败')
            }
          })
        } else {
          // 连接客户端失败
          console.log('连接客户端失败')
        }
      })
    },
    //视频创建
    createAllvideo() {
      let that = this
      this.createflag = false
      setTimeout(() => {
        that.createflag = true
      }, 100)
    },
    //视频设置
    setAllvideo() {
      let that = this
      this.setflag = false
      setTimeout(() => {
        that.setflag = true
      }, 100)
    },
    //视频销毁
    allVideoDestory() {
      let that = this
      this.destoryflag = false
      setTimeout(() => {
        that.destoryflag = true
      }, 100)
    },
    //视频登出
    logout() {
      this.ws.logout({
        loginIp: '*************',
      })
      this.isLogin = false
    },
    //视频点位点击
    getPointInfo(item) {
      if (this.loadStatus) {
        this.getBottomVideo(item)
      } else {
        this.info = item
        this.code = item.key
        this.videoDialogFlag = true
      }
    },
    //地图放大后底部视频
    getBottomVideo(obj) {
      let that = this
      if (this.listData.length < 6) {
        if (this.listData.length > 0) {
          if (this.isRepeat(this.listData, obj.key)) {
            this.listData.push(obj)
            this.codeList.push(obj.key)
          }
        } else {
          this.listData.push(obj)
          this.codeList.push(obj.key)
        }
      } else {
        this.listData.splice(0, 1)
        this.listData.push(obj)
        this.codeList.splice(0, 1)
        this.codeList.push(obj.key)
      }
      console.log('111', this.listData, '222', this.codeList)
      setTimeout(() => {
        that.loginVideo()
      }, 200)
    },
    //查重
    isRepeat(arr, key) {
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].key == key) {
          return false // 存在
        }
      }
      return true
    },
    //显示隐藏视频列表
    videoClick() {
      this.showVideoList = !this.showVideoList
    },
  },
  //监听值的变化
  watch: {
    // spjkShow(val){
    //   this.getYYVideo()
    // },
    hjShow(val) {
      if (val) {
        // this.loadLines()
        this.loadPoint([this.venuesList[4]], 54, 30, 'hjqd')
        this.loadPoint([this.venuesList[5]], 55, 30, 'hjzd')
        view.zoom = 13.566688671300383
        window.renderer = window.ArcGisUtils.addPathsLineEffect(view, {
          paths: [this.hjdata],
          color: '#E4393C',
          size: 4,
          length: 0.7,
          speed: 0.2,
          isShow: true,
        })
      } else {
        window.view.map.remove(window.layer54)
        window.view.map.remove(window.layer55)
        window.view.map.remove(window.hjlayer)
        window.ArcGisUtils.removePathsLineEffect(view, window.renderer)
      }
    },
    videoDialogFlag(val) {
      if (val) {
        this.showVenueVideo()
      } else {
        this.closeVideo()
      }
    },
  },
}
</script>

<style lang="less">
.map-container {
  position: relative;
  overflow: hidden;
  /* tree结构 */
  .spjkSelector {
    background-image: url('@/assets/img/common/图例bg316.png');
    position: absolute;
    top: 10px;
    left: 75px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    flex-direction: column;
  }
  .cgyjSelector {
    background-image: url('@/assets/img/common/图例316.png');
    position: absolute;
    top: 156px;
    left: 75px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    flex-direction: column;
  }
  .afdwSelector {
    background-image: url('@/assets/img/common/图例bg292.png');
    position: absolute;
    top: 10px;
    left: 75px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    flex-direction: column;
  }

  .el-input__inner {
    font-size: 30px !important;
    color: #ecebeb !important;
    height: 100%;
    border: none !important;
    background-color: transparent !important;
  } /* 搜索框叉的样式 */
  .el-input__suffix-inner .el-icon-circle-close.el-input__clear {
    /* margin-left: -190px; */
    margin-top: 3px;
    margin-right: -5px;
    color: #5ea8d3;
    font-size: 24px;
  }
  .el-checkbox__input {
    float: right;
    margin-right: 25px;
  }
  .el-tree-node__content {
    height: 15px !important;
    margin-bottom: 10px;
  }
  .is-focusable {
    background-color: unset;
  }
  .el-checkbox {
    display: block;
    border-radius: 15px;
    margin-bottom: 2px;
    margin-right: -20px;
  }
  /*这个是改变点击和悬浮背景颜色*/
  .el-checkbox-group .is-checked {
    background: linear-gradient(94deg, rgba(3, 97, 156, 0) 0%, #03619c 100%) !important;
    border-radius: 0px 30px 30px 0px;
  }
  .el-checkbox__label {
    font-size: 30px;
    font-weight: bold;
    color: #c0d6ed;
    line-height: 58px;
  }
  .el-checkbox__inner {
    width: 11px;
    height: 11px;
    margin-top: 5px;
    background-color: #344d67;
  }
  .checkbox-box-img {
    width: 30px;
    height: 42px;
    position: relative;
    top: 10px;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #252316;
    border-color: #ffc561;
  }

  .el-checkbox__inner::after {
    color: #ffc561 !important;
  }
  .shijian .contain::-webkit-scrollbar {
    /*滚动条整体样式*/
    width: 6px;
    /*高宽分别对应横竖滚动条的尺寸*/
    height: 1px;
  }
  .shijian .contain::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background: #20aeff;
    height: 8px;
  }
  .el-icon-caret-left:before {
    font-size: 20px;
  }
  /* tree结构 */
  .el-tree {
    background-color: unset;
    margin-top: 10px;
  }
  .el-tree-node__content {
    /* /这里是我改的 原本是50，目的是为了让加的那两个下拉显示出来 */
    height: 15px !important;
    margin-bottom: 10px;
    padding: 0 !important;
  }
  .el-tree-node.is-current > .el-tree-node__content,
  .el-tree-node__content:hover {
    background: linear-gradient(94deg, rgba(3, 97, 156, 0) 0%, #03619c 100%) !important;
    border-radius: 0px 30px 30px 0px;
  }
  .el-tree-node > .el-tree-node__children {
    margin-left: 6px;
  }
  .el-tree-node__content > label.el-checkbox {
    margin-right: -20px;
  }
  /*点击了小三角后改变背景颜色*/
  .el-tree-node:focus > .el-tree-node__content {
    background-color: #004874;
  }

  .yycgButton {
    position: absolute;
    top: 155px;
    left: 10px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    flex-direction: column;

    .yycg {
      width: 56px;
      height: 56px;
      cursor: pointer;
    }

    .yycg-icon {
      width: 20px;
      height: 20px;
      position: absolute;
      right: -5px;
      bottom: -5px;
      cursor: pointer;
    }
    .yycgText {
      font-size: 16px;
      font-family: YouSheBiaoTiHei-Bold, YouSheBiaoTiHei;
      font-weight: 400;
      color: #ffffff;
      line-height: 52px;
      text-shadow: 0px 4px 0px #04122f;
    }
  }
  .yjtzButton {
    position: absolute;
    top: 228px;
    left: 10px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    flex-direction: column;

    .yjtz {
      width: 56px;
      height: 56px;
      cursor: pointer;
    }

    .yjtz-icon {
      width: 20px;
      height: 20px;
      position: absolute;
      right: -5px;
      bottom: -5px;
      cursor: pointer;
    }
    .yjtzText {
      font-size: 16px;
      font-family: YouSheBiaoTiHei-Bold, YouSheBiaoTiHei;
      font-weight: 400;
      color: #ffffff;
      line-height: 52px;
      text-shadow: 0px 4px 0px #04122f;
    }
  }

  .spjkButton {
    position: absolute;
    top: 13px;
    left: 10px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    flex-direction: column;
    .spjk {
      width: 56px;
      height: 56px;
      cursor: pointer;
    }
    .spjk-icon {
      width: 20px;
      height: 20px;
      position: absolute;
      right: -5px;
      bottom: -5px;
      cursor: pointer;
    }

    .buttonText {
      font-size: 24px;
      font-family: YouSheBiaoTiHei-Bold, YouSheBiaoTiHei;
      font-weight: 400;
      color: #ffffff;
      line-height: 52px;
      text-shadow: 0px 4px 0px #04122f;
    }
  }
  .hjButton {
    position: absolute;
    top: 85px;
    left: 10px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    flex-direction: column;

    .hj {
      width: 56px;
      height: 56px;
      cursor: pointer;
    }

    .hj-icon {
      width: 20px;
      height: 20px;
      position: absolute;
      right: -5px;
      bottom: -5px;
      cursor: pointer;
    }
    .buttonText {
      font-size: 24px;
      font-family: YouSheBiaoTiHei-Bold, YouSheBiaoTiHei;
      font-weight: 400;
      color: #ffffff;
      line-height: 52px;
      text-shadow: 0px 4px 0px #04122f;
    }
  }
  .afdwButton {
    position: absolute;
    top: 10px;
    left: 10px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    flex-direction: column;

    .afdw {
      width: 56px;
      height: 56px;
      cursor: pointer;
    }

    .afdw-icon {
      width: 20px;
      height: 20px;
      position: absolute;
      right: -5px;
      bottom: -5px;
      cursor: pointer;
    }
    .buttonText {
      font-size: 24px;
      font-family: YouSheBiaoTiHei-Bold, YouSheBiaoTiHei;
      font-weight: 400;
      color: #ffffff;
      line-height: 52px;
      text-shadow: 0px 4px 0px #04122f;
    }
  }
  .full {
    position: absolute;
    top: 13px;
    right: 10px;
    cursor: pointer;
    .fullImg {
      width: 56px;
      height: 56px;
    }
  }

  .back {
    position: absolute;
    top: 158px;
    left: 49px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    cursor: pointer;
    .areaText {
      font-size: 22px;
      font-family: YouSheBiaoTiHei-Bold, YouSheBiaoTiHei;
      font-weight: 400;
      background: linear-gradient(180deg, #ffffff 32%, #52c3f7 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  .circleBox {
    position: absolute;
    // width: 300px;
    // height: 100px;
    margin: 10px auto;
    right: 82px;
    bottom: 128px;

    div.inner {
      width: 45px;
      height: 45px;
      background: #247cd9;
      border-radius: 50%;
      position: fixed;
      // right: 0;
      // top: 0;
      // left: 50%;
      // margin-left: -45px;
      // margin-top: 152px;
      // z-index: 10;
      animation-duration: 2.4s;
      -webkit-animation-name: state1;
      animation-name: state1;
      -webkit-animation-timing-function: linear;
      animation-timing-function: linear;
      -webkit-animation-iteration-count: infinite;
      animation-iteration-count: infinite;
      opacity: 0;
    }

    @keyframes state1 {
      0% {
        opacity: 0.5;
        -webkit-transform: scale(1);
        transform: scale(1);
      }

      100% {
        opacity: 0;
        border: 1px solid rgb(5, 1, 56);
        -webkit-transform: scale(4.5);
        transform: scale(4.5);
      }
    }

    .avatar {
      // position: fixed;
      // left: 50%;
      // margin-left: -28px;
      // margin-top: 172px;
      border-radius: 50%;
      width: 56px;
      height: 56px;
      background-color: transparent;
      // z-index: 11;
    }

    .container {
      animation-delay: 0.8s;
    }

    .outter {
      animation-delay: 1.5s;
    }
  }

  .zlimg {
    background-image: url('@/assets/img/common/zhiling1.png');
    background-size: cover;
    width: 146px;
    height: 147px;
    position: absolute;
    right: 40px;
    bottom: 40px;
    cursor: pointer;
    // -webkit-transform: rotate(360deg);
    animation: rotation 3s linear infinite;
    display: flex;
    align-items: center;
    justify-content: center;
    // -moz-animation: rotation 3s linear infinite;
    // -webkit-animation: rotation 3s linear infinite;
    // -o-animation: rotation 3s linear infinite;

    .zlimg1 {
      background-image: url('@/assets/img/common/zhiling2.png');
      background-size: cover;
      width: 113px;
      height: 113px;
      display: flex;
      align-items: center;
      justify-content: center;
      animation: rotation1 1.5s linear infinite;
      border-radius: 100%;

      .imgMsg {
        animation: rotation2 3s linear infinite;
      }
    }
  }

  @keyframes rotation {
    from {
      -webkit-transform: rotate(0deg);
    }

    to {
      -webkit-transform: rotate(360deg);
    }
  }

  @keyframes rotation1 {
    from {
      -webkit-transform: rotate(0deg);
    }

    to {
      -webkit-transform: rotate(-360deg);
    }
  }

  @keyframes rotation2 {
    from {
      -webkit-transform: rotate(-360deg);
    }

    to {
      -webkit-transform: rotate(0deg);
    }
  }

  .radioDiv {
    position: absolute;
    width: 115px;
    background: linear-gradient(180deg, rgba(14, 26, 64, 0.8) 0%, rgba(6, 64, 105, 0.8) 100%);
    box-sizing: border-box;
    border: 1px solid #0b64c3;
    box-shadow: inset 0px 0px 10px 0px rgba(14, 156, 255, 0.7);
    left: 18px;
    bottom: 14px;
    padding-left: 14px;
    padding-top: 14px;
    box-sizing: border-box;

    .radioitem {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      cursor: pointer;

      > div:first-child {
        width: 14px;
        height: 14px;
        border-radius: 2px 2px 2px 2px;
        opacity: 1;
        border: 1px solid #ffffff;
      }

      > div:nth-child(2) {
        margin-left: 9px;
        font-size: 16px;
        font-family: 'DIN-Medium';
        font-weight: 400;
        color: #ffffff;
        line-height: 14px;
        -webkit-background-clip: text;
      }
    }

    .radioActive {
      // background: rgba(139, 110, 0, 0.2);
      border-radius: 2px 2px 2px 2px;
      opacity: 1;
      border: 1px solid;
      border-image: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(255, 178.45999270677567, 0, 1)) 1 1 !important;
      background: linear-gradient(to top, #ffdc00, #fffcf3);
      -webkit-background-clip: text;
      color: transparent;
      text-align: center;
      line-height: 14px;
      font-weight: 700;
      cursor: pointer;
    }
  }
}

/*点位label样式*/
/deep/ .map-container {
  .amap-marker-label {
    background: #00b6f3;
  }
}

.video_box {
  margin: 24px;
  display: flex;
  .video_item {
    width: 675px;
    height: 675px;
    background: url('@/assets/img/home/<USER>') 0 0 no-repeat;
    background-size: cover;
  }
  .line {
    width: 2px;
    height: 674px;
    background: url('@/assets/img/home/<USER>') 0 0 no-repeat;
    background-size: cover;
    margin-left: 28px;
  }
  .infos {
    margin-left: 34px;
    .info {
      .name {
        font-size: 18px;
        font-family: Source Han Sans CN-Regular, Source Han Sans CN;
        font-weight: 400;
        color: #91d1f4;
        line-height: 40px;
      }
      .value {
        font-size: 24px;
        font-family: Source Han Sans CN-Medium, Source Han Sans CN;
        font-weight: 500;
        color: #ffffff;
        line-height: 40px;
      }
    }
  }
  .jiajian {
    display: flex;
    margin-left: 90px;
    margin-top: 385px;
    .titleName {
      max-width: 1100px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
.jiajian1 {
  display: flex;
  margin-left: 60px;
  margin-top: 30px;
  .titleName {
    max-width: 1100px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.video_list_bottom {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  .show_more {
    width: 1900px;
    height: 63px;
    background: url('@/assets/img/map/video_show.png') 0 0 no-repeat;
    background-size: cover;
    box-sizing: border-box;
    .txt {
      width: 472px;
      height: 57px;
      background: url('@/assets/img/map/video_show_btn.png') 0 0 no-repeat;
      background-size: cover;
      margin: 0 auto;
    }
  }
  .video_list {
    width: 1900px;
    height: 303px;
    background: url('@/assets/img/map/video_list_box.png') 0 0 no-repeat;
    background-size: cover;
    .video_list_btn {
      width: 472px;
      height: 58px;
      background: url('@/assets/img/map/video_list_box_btn.png') 0 0 no-repeat;
      background-size: cover;
      margin: 0 auto;
    }
    .video_bottom_box {
      display: flex;
      align-content: center;
      align-items: center;
      .video_bottom_item {
        position: relative;
        width: 280px;
        height: 210px;
        margin-left: 20px;
        margin-right: 16px;
        // background: rgba(0, 0, 0, 0.5);
        .video_btn {
          width: 24px;
          height: 24px;
          position: absolute;
          top: 16px;
          right: 16px;
        }
      }
    }
  }
}
</style>
